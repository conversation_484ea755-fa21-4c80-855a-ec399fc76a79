# 4. 核心实现：统一AOP与分片路由引擎

## 4.1 统一AOP切面设计

### 4.1.1 切面架构与职责划分

为了实现分库分表的统一管理，我们设计了一个职责清晰、可扩展的AOP架构。核心原则是**"切面只做调度，引擎负责执行"**，确保代码的可测试性和可维护性。

```java
@Aspect
@Component
@Order(1)
public class UnifiedShardingAspect {
    
    private static final Logger log = LoggerFactory.getLogger(UnifiedShardingAspect.class);
    
    @Autowired
    private ShardingExecutionEngine executionEngine;
    
    @Around("execution(* com.example.dao.*.*(..))")
    public Object handleSharding(ProceedingJoinPoint point) throws Throwable {
        // 1. 构建查询上下文
        QueryContext context = buildQueryContext(point);
        
        // 2. 委托给执行引擎处理
        return executionEngine.execute(context, point);
    }
    
    private QueryContext buildQueryContext(ProceedingJoinPoint point) {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        
        return QueryContext.builder()
                .methodName(method.getName())
                .methodSignature(signature)
                .args(point.getArgs())
                .returnType(determineReturnType(method))
                .operationType(determineOperationType(method))
                .guaranteeNo(ServiceContext.getGuaranteeNo())
                .build();
    }
    
    private ReturnType determineReturnType(Method method) {
        Class<?> returnType = method.getReturnType();
        
        if (returnType == void.class) {
            return ReturnType.VOID;
        } else if (Collection.class.isAssignableFrom(returnType)) {
            return ReturnType.LIST;
        } else if (returnType.getSimpleName().contains("Page")) {
            return ReturnType.PAGE;
        } else if (isPrimitiveOrWrapper(returnType)) {
            return ReturnType.AGGREGATION;
        } else {
            return ReturnType.SINGLE_OBJECT;
        }
    }
    
    private OperationType determineOperationType(Method method) {
        String methodName = method.getName().toLowerCase();
        
        if (methodName.startsWith("insert") || methodName.startsWith("save")) {
            return OperationType.INSERT;
        } else if (methodName.startsWith("update") || methodName.startsWith("modify")) {
            return OperationType.UPDATE;
        } else if (methodName.startsWith("delete") || methodName.startsWith("remove")) {
            return OperationType.DELETE;
        } else {
            return OperationType.SELECT;
        }
    }
}
```

### 4.1.2 上下文信息提取与传递

查询上下文是整个分片路由的核心数据结构，包含了路由决策所需的所有信息：

```java
@Data
@Builder
public class QueryContext {
    // 基础信息
    private String methodName;
    private MethodSignature methodSignature;
    private Object[] args;
    private ReturnType returnType;
    private OperationType operationType;
    
    // 路由信息
    private String guaranteeNo;        // 分库键
    private String shardingKey;        // 分表键
    private boolean hasShardingKey;    // 是否包含分表键
    
    // 查询特征
    private String sql;                // 实际SQL（如果可获取）
    private boolean hasPagination;     // 是否分页查询
    private boolean isRandomPagination; // 是否随机分页
    private int pageSize;              // 分页大小
    private int forcedLimit;           // 强制限制条数
    
    // 查询条件特征
    private boolean hasOrderBy;
    private boolean hasMultipleConditions;
    private boolean hasRangeCondition;
    private boolean hasLikeCondition;
    private boolean hasJoin;
    private boolean hasSubQuery;
    private boolean hasAggregation;
    
    public boolean requiresIndexTable() {
        return operationType == OperationType.SELECT && 
               !hasShardingKey && 
               returnType != ReturnType.VOID;
    }
    
    public boolean isWriteOperation() {
        return operationType == OperationType.INSERT || 
               operationType == OperationType.UPDATE || 
               operationType == OperationType.DELETE;
    }
}

enum ReturnType {
    VOID, SINGLE_OBJECT, LIST, PAGE, AGGREGATION
}

enum OperationType {
    INSERT, UPDATE, DELETE, SELECT
}
```

### 4.1.3 分库分表路由的统一入口

执行引擎是整个分片系统的核心，负责根据查询上下文选择合适的处理策略：

```java
@Service
@Transactional(propagation = Propagation.REQUIRED)
public class ShardingExecutionEngine {
    
    private static final Logger log = LoggerFactory.getLogger(ShardingExecutionEngine.class);
    
    @Autowired
    private WriteOperationHandler writeHandler;
    
    @Autowired
    private ReadOperationHandler readHandler;
    
    @Autowired
    private QueryStrategyManager strategyManager;
    
    public Object execute(QueryContext context, ProceedingJoinPoint point) throws Throwable {
        try {
            // 记录执行开始
            long startTime = System.currentTimeMillis();
            
            Object result;
            if (context.isWriteOperation()) {
                result = writeHandler.handle(context, point);
            } else {
                result = readHandler.handle(context, point);
            }
            
            // 记录执行统计
            recordExecutionMetrics(context, System.currentTimeMillis() - startTime);
            
            return result;
            
        } catch (Exception e) {
            log.error("Sharding execution failed: method={}, error={}", 
                     context.getMethodName(), e.getMessage(), e);
            throw e;
        }
    }
    
    private void recordExecutionMetrics(QueryContext context, long executionTime) {
        // 记录到监控系统
        String metricName = String.format("sharding.%s.%s", 
                                         context.getOperationType().name().toLowerCase(),
                                         context.getReturnType().name().toLowerCase());
        
        Metrics.timer(metricName).record(executionTime, TimeUnit.MILLISECONDS);
    }
}
```

## 4.2 HintManager强制路由实现

### 4.2.1 基于ServiceContext的路由策略

HintManager是ShardingSphere提供的强制路由机制，我们基于ServiceContext中的上下文信息来实现精确路由：

```java
@Component
public class HintRoutingManager {
    
    private static final Logger log = LoggerFactory.getLogger(HintRoutingManager.class);
    
    public void applyDatabaseHint(String guaranteeNo) {
        if (StringUtils.isBlank(guaranteeNo)) {
            throw new ShardingException("Guarantee number is required for database routing");
        }
        
        try (HintManager hintManager = HintManager.getInstance()) {
            // 设置分库路由
            hintManager.setDatabaseShardingValue("t_order", "guarantee_no", guaranteeNo);
            
            log.debug("Applied database hint: guarantee_no={}", guaranteeNo);
        }
    }
    
    public void applyTableHint(String tableName, String shardingKey) {
        if (StringUtils.isBlank(shardingKey)) {
            log.debug("No table sharding key provided, skipping table hint");
            return;
        }
        
        try (HintManager hintManager = HintManager.getInstance()) {
            // 设置分表路由
            hintManager.setTableShardingValue(tableName, "sharding_key", shardingKey);
            
            log.debug("Applied table hint: table={}, sharding_key={}", tableName, shardingKey);
        }
    }
    
    public void applyBothHints(String guaranteeNo, String tableName, String shardingKey) {
        if (StringUtils.isBlank(guaranteeNo)) {
            throw new ShardingException("Guarantee number is required for routing");
        }
        
        try (HintManager hintManager = HintManager.getInstance()) {
            // 设置分库路由
            hintManager.setDatabaseShardingValue("t_order", "guarantee_no", guaranteeNo);
            
            // 设置分表路由（如果有分表键）
            if (StringUtils.isNotBlank(shardingKey)) {
                hintManager.setTableShardingValue(tableName, "sharding_key", shardingKey);
            }
            
            log.debug("Applied both hints: guarantee_no={}, table={}, sharding_key={}", 
                     guaranteeNo, tableName, shardingKey);
        }
    }
}
```

### 4.2.2 分库路由：融担号的处理

分库路由基于融担号进行，这是系统的基础路由机制：

```java
@Component
public class DatabaseRoutingHandler {
    
    @Autowired
    private HintRoutingManager hintManager;
    
    public void routeToDatabase(QueryContext context) {
        String guaranteeNo = context.getGuaranteeNo();
        
        if (StringUtils.isBlank(guaranteeNo)) {
            // 尝试从ServiceContext获取
            guaranteeNo = ServiceContext.getGuaranteeNo();
        }
        
        if (StringUtils.isBlank(guaranteeNo)) {
            throw new ShardingException("Cannot determine database route: no guarantee number available");
        }
        
        // 应用分库路由
        hintManager.applyDatabaseHint(guaranteeNo);
        
        // 记录路由信息
        context.setGuaranteeNo(guaranteeNo);
    }
}
```

### 4.2.3 分表路由：分片键的动态获取

分表路由需要根据具体的业务场景动态获取分片键：

```java
@Component
public class TableRoutingHandler {
    
    @Autowired
    private HintRoutingManager hintManager;
    
    @Autowired
    private ShardingKeyExtractor keyExtractor;
    
    public void routeToTable(QueryContext context, String tableName) {
        // 1. 尝试从方法参数中提取分片键
        String shardingKey = keyExtractor.extractFromArgs(context);
        
        if (StringUtils.isBlank(shardingKey)) {
            // 2. 尝试从SQL中解析分片键
            shardingKey = keyExtractor.extractFromSql(context.getSql());
        }
        
        if (StringUtils.isNotBlank(shardingKey)) {
            // 应用分表路由
            hintManager.applyTableHint(tableName, shardingKey);
            context.setShardingKey(shardingKey);
            context.setHasShardingKey(true);
            
            log.debug("Table routing applied: table={}, sharding_key={}", tableName, shardingKey);
        } else {
            log.debug("No sharding key found, table routing skipped");
            context.setHasShardingKey(false);
        }
    }
}

@Component
public class ShardingKeyExtractor {
    
    public String extractFromArgs(QueryContext context) {
        Object[] args = context.getArgs();
        
        if (args == null || args.length == 0) {
            return null;
        }
        
        // 根据方法类型提取分片键
        switch (context.getOperationType()) {
            case INSERT:
            case UPDATE:
                return extractFromEntity(args[0]);
            case SELECT:
                return extractFromSelectArgs(args);
            default:
                return null;
        }
    }
    
    private String extractFromEntity(Object entity) {
        if (entity == null) {
            return null;
        }
        
        try {
            // 使用反射获取ID字段
            Field idField = entity.getClass().getDeclaredField("id");
            idField.setAccessible(true);
            Object idValue = idField.get(entity);
            return idValue != null ? idValue.toString() : null;
        } catch (Exception e) {
            log.debug("Failed to extract sharding key from entity: {}", e.getMessage());
            return null;
        }
    }
    
    private String extractFromSelectArgs(Object[] args) {
        // 查找第一个String类型的参数作为可能的ID
        for (Object arg : args) {
            if (arg instanceof String && isValidId((String) arg)) {
                return (String) arg;
            }
        }
        return null;
    }
    
    private boolean isValidId(String value) {
        // 简单的ID格式验证
        return StringUtils.isNotBlank(value) && value.length() > 10;
    }
    
    public String extractFromSql(String sql) {
        if (StringUtils.isBlank(sql)) {
            return null;
        }
        
        // 使用正则表达式从SQL中提取ID条件
        Pattern pattern = Pattern.compile("id\\s*=\\s*['\"]([^'\"]+)['\"]", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(sql);
        
        if (matcher.find()) {
            return matcher.group(1);
        }
        
        return null;
    }
}
```

## 4.3 事务一致性保障机制

### 4.3.1 主表与索引表的原子性写入

为了确保主表和索引表的数据一致性，我们设计了统一的写操作处理器，在同一个事务中完成两张表的操作：

```java
@Service
@Transactional(propagation = Propagation.REQUIRED)
public class WriteOperationHandler {

    private static final Logger log = LoggerFactory.getLogger(WriteOperationHandler.class);

    @Autowired
    private DatabaseRoutingHandler dbRoutingHandler;

    @Autowired
    private TableRoutingHandler tableRoutingHandler;

    @Autowired
    private IndexTableManager indexTableManager;

    public Object handle(QueryContext context, ProceedingJoinPoint point) throws Throwable {
        // 1. 应用分库路由
        dbRoutingHandler.routeToDatabase(context);

        // 2. 应用分表路由
        String tableName = extractTableName(context);
        tableRoutingHandler.routeToTable(context, tableName);

        // 3. 执行主表操作
        Object result = point.proceed();

        // 4. 同步更新索引表
        if (needsIndexTableUpdate(context)) {
            updateIndexTable(context, result);
        }

        return result;
    }

    private boolean needsIndexTableUpdate(QueryContext context) {
        return context.getOperationType() == OperationType.INSERT ||
               context.getOperationType() == OperationType.UPDATE;
    }

    private void updateIndexTable(QueryContext context, Object result) {
        try {
            switch (context.getOperationType()) {
                case INSERT:
                    indexTableManager.insertIndexRecord(context, result);
                    break;
                case UPDATE:
                    indexTableManager.updateIndexRecord(context, result);
                    break;
                default:
                    // DELETE操作暂不处理索引表
                    break;
            }
        } catch (Exception e) {
            log.error("Failed to update index table: method={}, error={}",
                     context.getMethodName(), e.getMessage(), e);
            // 抛出异常，触发事务回滚
            throw new IndexTableUpdateException("Index table update failed", e);
        }
    }

    private String extractTableName(QueryContext context) {
        // 从方法名或注解中提取表名
        String methodName = context.getMethodName();
        if (methodName.contains("Order")) {
            return "t_order";
        }
        // 可以扩展其他表的处理
        return "t_order"; // 默认表名
    }
}
```

### 4.3.2 索引表管理器

索引表管理器负责维护索引表的数据同步：

```java
@Component
public class IndexTableManager {

    private static final Logger log = LoggerFactory.getLogger(IndexTableManager.class);

    @Autowired
    private IndexTableMapper indexTableMapper;

    @Value("${index.table.enabled:true}")
    private boolean indexTableEnabled;

    public void insertIndexRecord(QueryContext context, Object result) {
        if (!indexTableEnabled) {
            return;
        }

        IndexRecord record = buildIndexRecord(context, result);
        if (record != null) {
            indexTableMapper.insert(record);
            log.debug("Index record inserted: {}", record);
        }
    }

    public void updateIndexRecord(QueryContext context, Object result) {
        if (!indexTableEnabled) {
            return;
        }

        IndexRecord record = buildIndexRecord(context, result);
        if (record != null) {
            // 先尝试更新，如果不存在则插入
            int updated = indexTableMapper.updateById(record);
            if (updated == 0) {
                indexTableMapper.insert(record);
                log.debug("Index record inserted (update failed): {}", record);
            } else {
                log.debug("Index record updated: {}", record);
            }
        }
    }

    private IndexRecord buildIndexRecord(QueryContext context, Object result) {
        try {
            // 从结果或参数中提取索引信息
            Object entity = extractEntity(context, result);
            if (entity == null) {
                return null;
            }

            return IndexRecord.builder()
                    .id(getFieldValue(entity, "id"))
                    .userId(getFieldValue(entity, "userId"))
                    .orderNo(getFieldValue(entity, "orderNo"))
                    .phone(getFieldValue(entity, "phone"))
                    .createTime(getFieldValue(entity, "createTime"))
                    .build();

        } catch (Exception e) {
            log.warn("Failed to build index record: {}", e.getMessage());
            return null;
        }
    }

    private Object extractEntity(QueryContext context, Object result) {
        // 从方法参数中获取实体对象
        Object[] args = context.getArgs();
        if (args != null && args.length > 0) {
            return args[0]; // 通常第一个参数是实体对象
        }
        return null;
    }

    private String getFieldValue(Object entity, String fieldName) {
        try {
            Field field = entity.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            Object value = field.get(entity);
            return value != null ? value.toString() : null;
        } catch (Exception e) {
            return null;
        }
    }
}

@Data
@Builder
public class IndexRecord {
    private String id;
    private String userId;
    private String orderNo;
    private String phone;
    private Date createTime;
}
```

### 4.3.3 事务传播级别的控制

为了确保事务的正确传播，我们需要精确控制事务边界：

```java
@Configuration
@EnableTransactionManagement
public class TransactionConfig {

    @Bean
    public PlatformTransactionManager transactionManager(DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean
    public TransactionTemplate transactionTemplate(PlatformTransactionManager transactionManager) {
        TransactionTemplate template = new TransactionTemplate(transactionManager);
        template.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        template.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        template.setTimeout(30); // 30秒超时
        return template;
    }
}

@Component
public class TransactionHelper {

    @Autowired
    private TransactionTemplate transactionTemplate;

    public <T> T executeInTransaction(Supplier<T> operation) {
        return transactionTemplate.execute(status -> {
            try {
                return operation.get();
            } catch (Exception e) {
                status.setRollbackOnly();
                throw new RuntimeException("Transaction execution failed", e);
            }
        });
    }

    public void executeInTransaction(Runnable operation) {
        transactionTemplate.execute(status -> {
            try {
                operation.run();
                return null;
            } catch (Exception e) {
                status.setRollbackOnly();
                throw new RuntimeException("Transaction execution failed", e);
            }
        });
    }
}
```

## 4.4 分片算法的具体实现

### 4.4.1 自定义Hint分片算法

我们实现了基于Hint的分片算法，支持从上下文获取路由信息：

```java
public class GuaranteeNoHintShardingAlgorithm implements HintShardingAlgorithm<String> {

    private static final Logger log = LoggerFactory.getLogger(GuaranteeNoHintShardingAlgorithm.class);

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames,
                                       HintShardingValue<String> shardingValue) {

        String guaranteeNo = extractGuaranteeNo(shardingValue);

        if (StringUtils.isBlank(guaranteeNo)) {
            throw new ShardingException("Guarantee number is required for database sharding");
        }

        // 计算目标数据库
        int dbIndex = calculateDatabaseIndex(guaranteeNo, availableTargetNames.size());

        String targetDb = availableTargetNames.stream()
                .filter(name -> name.endsWith("_" + dbIndex))
                .findFirst()
                .orElseThrow(() -> new ShardingException("No target database found for index: " + dbIndex));

        log.debug("Database sharding: guarantee_no={}, target={}", guaranteeNo, targetDb);

        return Collections.singletonList(targetDb);
    }

    private String extractGuaranteeNo(HintShardingValue<String> shardingValue) {
        // 优先从Hint值获取
        if (!shardingValue.getValues().isEmpty()) {
            return shardingValue.getValues().iterator().next();
        }

        // 从上下文获取（兜底方案）
        return ServiceContext.getGuaranteeNo();
    }

    private int calculateDatabaseIndex(String guaranteeNo, int dbCount) {
        return Math.abs(guaranteeNo.hashCode()) % dbCount;
    }
}
```

### 4.4.2 分表算法：业务分片键处理

分表算法支持多种分片键的处理：

```java
public class BusinessKeyHintShardingAlgorithm implements HintShardingAlgorithm<String> {

    private static final Logger log = LoggerFactory.getLogger(BusinessKeyHintShardingAlgorithm.class);

    private static final int TABLE_COUNT = 128;

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames,
                                       HintShardingValue<String> shardingValue) {

        String shardingKey = extractShardingKey(shardingValue);

        if (StringUtils.isBlank(shardingKey)) {
            // 如果没有分表键，返回所有表（广播查询）
            log.warn("No sharding key provided, broadcasting to all tables");
            return availableTargetNames;
        }

        // 计算目标表
        int tableIndex = calculateTableIndex(shardingKey);

        String targetTable = availableTargetNames.stream()
                .filter(name -> name.endsWith("_" + tableIndex))
                .findFirst()
                .orElseThrow(() -> new ShardingException("No target table found for index: " + tableIndex));

        log.debug("Table sharding: sharding_key={}, target={}", shardingKey, targetTable);

        return Collections.singletonList(targetTable);
    }

    private String extractShardingKey(HintShardingValue<String> shardingValue) {
        if (!shardingValue.getValues().isEmpty()) {
            return shardingValue.getValues().iterator().next();
        }
        return null;
    }

    private int calculateTableIndex(String shardingKey) {
        return Math.abs(shardingKey.hashCode()) % TABLE_COUNT;
    }
}
```
