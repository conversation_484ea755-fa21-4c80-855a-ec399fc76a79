# 3. 库内分表与热数据索引方案

## 3.1 分表的必要性

随着业务的持续高速增长，尽管现有的分库机制有效分散了整体数据压力，但新的性能瓶颈在**分库内部**浮现。部分融担数据高度集中，导致其所在的单个物理分库中，核心业务表（如 `t_trans_order`）的数据量再次达到性能极限，单表数据规模已达数亿级别。

这种库内单表的过度膨胀，已引发以下核心痛点：

+ **查询性能急剧下降：** 对大表的查询，尤其是非主键查询和分页查询，响应时间显著增长，严重影响用户体验。
+ **数据库维护困难：** 对大表的DDL操作（如加索引、改字段）耗时漫长，锁表风险高，数据库的日常维护和迭代变得异常困难。
+ **潜在的稳定性风险：** 大规模的慢查询持续消耗数据库资源，对整个分库的稳定性构成严重威胁。

**解决方案：** 在现有成熟、稳定的分库体系之上，引入**库内分表**（Table Sharding）机制。通过将单库内的大表进一步水平拆分为多张物理子表，将单点的数据压力和查询负载均匀分散。

## 3.2 分表策略设计

### 3.2.1 分片键选择

+ **分库键:** 融担号维持不变，以确保对现有分库逻辑的兼容性。
+ **库内分表键:** 
    - **优先使用：** 服务级统一分片键探索。在单个服务内部，寻找一个"黄金业务分表键"。这个分表键可以作为该服务内所有核心表的统一分片标准，这样所有的核心查询和修改都可以通过分表键精准路由物理表。然而，此类普适的"黄金分片键"通常难以找到。
    - **降级到单表：** 若确认不存在则降级为单表级别。鉴于存量业务的复杂性，基于不改业务的逻辑，将分表键的决策粒度被细化至每一张独立的数据表。对于每张待分表的表，以及对于业务的理解，需要考虑的问题是："对于这张表，用哪个字段做分片键，能让最多的核心查询语句最高效？"

通过此层决策，项目内绝大多数的表都将拥有一个最**适合自身业务场景的分片键**。

### 3.2.2 分表数量规划

+ **分表数量：** 综合考虑未来2-3年的业务增长、单表容量以及DDL广播效率，确定单库内分表数量为 **128** 张。

+ **单表数据量压力评估**
    - **5亿存量 +日增300万**
        * **两年后单库总数据量:** `300万/天 * 720天 ≈ 21亿`
        * **单表承载数据量:** `21亿 / 128张表 ≈` **1687.5万**
        * **结论:** 单表数据量低于2000万的理想值，此风险被认为是可接受的。
    - **10亿存量 + 日增 1000万**
        * **两年后单库总数据量:** `1000万/天 * 720天 ≈ 72亿`
        * **单表承载数据量:** `72亿 / 128张表 ≈` **5625万**
        * **结论:** 单表数据量高于2000万的理想值，不增加分表的前提下, 需要减少存储时长, 不过此类表较少, 目前支付模块仅trans_order表在8亿存量+900w增量左右, 其他系统未做调研。

### 3.2.3 分片算法实现

采用哈希取模算法确保数据均匀分布：

```java
public class HashModShardingAlgorithm implements StandardShardingAlgorithm<String> {
    
    private static final int SHARD_COUNT = 128;
    
    @Override
    public String doSharding(Collection<String> availableTargetNames, 
                           ShardingValue<String> shardingValue) {
        String value = shardingValue.getValue();
        int hashCode = value.hashCode();
        int shardIndex = Math.abs(hashCode) % SHARD_COUNT;
        
        return findTargetByIndex(availableTargetNames, shardIndex);
    }
    
    private String findTargetByIndex(Collection<String> targets, int index) {
        return targets.stream()
                .filter(target -> target.endsWith("_" + index))
                .findFirst()
                .orElseThrow(() -> new IllegalStateException("No target found for index: " + index));
    }
}
```

## 3.3 非分表键查询的性能挑战

分表后，所有不带分片键的查询将无法路由到具体的物理表，面临以下问题：

+ **全表扫描风险：** ShardingSphere在无分片键时，默认会对所有分片执行广播查询，在128个分片上并行执行，瞬间对数据库造成巨大I/O和连接池压力。
+ **性能急剧下降：** 原本的单表查询变成了128个表的并行查询，响应时间和资源消耗成倍增长。
+ **系统稳定性威胁：** 大量的广播查询可能引发数据库雪崩，严重威胁系统稳定性。

**传统解决方案的局限性：**
- 外部搜索引擎（如ES）：增加系统复杂度，存在数据一致性问题
- 冗余字段：业务侵入性强，维护成本高
- 中间件路由表：需要额外的存储和维护成本

## 3.4 热数据索引表解决方案

### 3.4.1 热数据模型

基于业务访问模式的分析，我们发现一个重要规律：**最近产生的数据具有最高的访问频率**。例如：
- 90%以上的订单查询集中在最近2周的数据
- 用户主要关注最新的交易记录和状态
- 历史数据的访问频率随时间呈指数级下降

基于这个**热数据模型**，我们设计了一个轻量级的索引表方案。

### 3.4.2 索引表设计

我们创建一张普通的单表 `t_order_index`，专门存储**非分片键到分片键的映射关系**：

```sql
CREATE TABLE `t_order_index` (
  `id` VARCHAR(64) NOT NULL COMMENT '订单ID，与主表ID一致',
  `user_id` VARCHAR(32) NOT NULL COMMENT '用户ID',
  `order_no` VARCHAR(64) NOT NULL COMMENT '订单号', 
  `phone` VARCHAR(20) COMMENT '手机号',
  `create_time` DATETIME NOT NULL COMMENT '创建时间',
  
  PRIMARY KEY (`id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_order_no` (`order_no`),
  INDEX `idx_phone` (`phone`),
  INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB COMMENT='订单热数据索引表';
```

**设计要点：**
- **普通单表结构**：无分区，结构简单，维护方便
- **映射关系存储**：存储业务查询中常用的非分片键字段
- **主键就是分片键**：通过ID可以直接路由到具体分表
- **精简字段**：只存储查询必需的字段，保持表结构轻量

### 3.4.3 查询流程

所有非分片键查询都通过两阶段查询完成：

1. **第一阶段：索引表查询**
   ```sql
   -- 根据用户ID查询获取订单ID列表
   SELECT id FROM t_order_index WHERE user_id = 'user123';
   ```

2. **第二阶段：精确分表查询**
   ```sql
   -- 根据ID精确路由到具体分表查询完整数据
   SELECT * FROM t_order WHERE id IN ('order1', 'order2', 'order3');
   ```

**性能优势：**
- 索引表查询：基于索引的高效查询，毫秒级响应
- 分表查询：通过分片键精确路由，避免广播查询
- 整体性能：两次精确查询的总耗时远低于128个分片的广播查询

## 3.5 索引表的生命周期管理

### 3.5.1 数据保留策略与热度统计

+ **动态配置的时间窗口：** 索引表只保留最近一段时间的热数据，通过配置中心动态设置保留时间。
+ **热度统计机制：**
  - 可选开启的查询热度统计功能
  - 记录不同时间范围内的查询频率分布
  - 为时间窗口优化提供数据支撑
  - 通过配置控制是否启用，避免对性能产生影响

+ **配置化管理：** 通过配置中心统一管理数据保留时间，支持动态调整。

```properties
# 索引表数据保留配置
index.table.retention.days=14
index.table.cleanup.schedule=0 2 * * *
index.table.stats.enabled=true
index.table.stats.sample.rate=0.01
```

**热度统计实现：**

```java
@Component
public class QueryHeatStatisticsCollector {

    @Value("${index.table.stats.enabled:false}")
    private boolean statsEnabled;

    @Value("${index.table.stats.sample.rate:0.01}")
    private double sampleRate;

    public void recordQuery(Date queryTime) {
        if (!statsEnabled || !shouldSample()) {
            return;
        }

        // 记录查询时间分布
        int daysAgo = calculateDaysFromNow(queryTime);
        incrementQueryCount(daysAgo);
    }

    private boolean shouldSample() {
        return Math.random() < sampleRate;
    }

    // 定期分析并推荐最优时间窗口
    @Scheduled(cron = "0 0 1 * * ?")
    public void analyzeAndRecommend() {
        if (!statsEnabled) {
            return;
        }

        Map<Integer, Long> distribution = getQueryDistribution();
        int recommendedDays = calculateOptimalRetentionDays(distribution);
        log.info("Recommended retention days: {}", recommendedDays);
    }
}
```

### 3.5.2 定时清理脚本

设计自动化的数据清理机制：

```sql
-- 清理脚本示例
DELETE FROM t_order_index
WHERE create_time < DATE_SUB(NOW(), INTERVAL ${retention_days} DAY)
LIMIT 10000;
```

**清理策略：**
- **分批删除：** 每次删除固定数量的记录，避免长时间锁表
- **定时执行：** 在业务低峰期执行，减少对线上业务的影响
- **监控告警：** 监控清理脚本的执行状态，确保数据生命周期管理正常

### 3.5.3 查询场景与降级策略

基于系统简洁性和性能考虑，我们对查询场景做了明确的边界定义：

**支持的查询场景：**
- **单点精确查询**：如根据订单号、用户ID等单一条件的精确匹配
- **覆盖80%以上的高频业务场景**

**降级为全表扫描的场景：**
- 复合条件查询（多字段组合）
- 范围查询（时间区间等）
- 模糊查询（LIKE操作）
- 索引表未命中的历史数据查询

**降级策略实现：**

```java
@Component
public class QueryDegradationManager {

    @Value("${query.degradation.max.tables:32}")
    private int maxScanTables;

    @Value("${query.degradation.timeout.seconds:10}")
    private int queryTimeoutSeconds;

    @Value("${query.degradation.max.results:100}")
    private int maxResults;

    public boolean shouldDegrade(QueryContext context) {
        // 1. 检查是否为单点查询
        if (!isSinglePointQuery(context)) {
            return true;
        }

        // 2. 检查是否命中索引表时间窗口
        if (!isWithinIndexTimeWindow(context)) {
            return true;
        }

        return false;
    }

    public void applyDegradationLimits(Statement stmt) {
        // 设置查询超时
        stmt.setQueryTimeout(queryTimeoutSeconds);

        // 设置最大返回结果数
        stmt.setMaxRows(maxResults);

        // 记录降级查询统计
        recordDegradedQuery();
    }
}
```

**降级查询监控：**

```java
@Component
public class DegradedQueryMonitor {

    private Counter degradedQueryCounter;
    private Timer degradedQueryTimer;

    @PostConstruct
    public void init() {
        degradedQueryCounter = meterRegistry.counter("query.degraded.count");
        degradedQueryTimer = meterRegistry.timer("query.degraded.time");
    }

    public void recordDegradedQuery(String queryType, long executionTime) {
        degradedQueryCounter.increment();

        // 记录执行时间
        degradedQueryTimer.record(executionTime, TimeUnit.MILLISECONDS);

        // 超过阈值告警
        if (executionTime > alertThresholdMs) {
            alertSlowDegradedQuery(queryType, executionTime);
        }
    }
}
```

### 3.5.4 不支持场景的替代方案

对于索引表不适合支持的查询场景，我们提供以下替代方案：

1. **数据仓库查询**：复杂的历史数据分析和报表查询应通过数据仓库系统实现
2. **离线批处理**：大范围的数据导出和统计应使用离线批处理作业
3. **专门的查询服务**：为运营和客服等特殊场景提供独立的查询服务，可以有更宽松的资源限制

通过这套热数据索引表方案，我们在保持系统简洁性的同时，有效解决了分表后非分片键查询的性能问题，为绝大多数业务场景提供了高效的查询能力，同时为特殊场景提供了明确的替代方案。
